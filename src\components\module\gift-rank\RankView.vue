<template>
  <div class="gift-rank-box">
    <div class="top-box">
      <div class="rank-top">
        <div class="logo">
          <img src="@/static/header/<EMAIL>" />
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">用户名<img class="sex" src="@/static/live/man-icon.png" /></div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="rank-top">
        <div class="logo">
          <img src="@/static/header/<EMAIL>" />
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">用户名<img class="sex" src="@/static/live/man-icon.png" /></div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="rank-top">
        <div class="logo">
          <img src="@/static/header/<EMAIL>" />
          <img class="badge" src="@/static/live/<EMAIL>" />
        </div>
        <div class="info">用户名<img class="sex" src="@/static/live/woman-icon.png" /></div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
    </div>
    <div class="list-box">
      <div class="item">
        <span class="num">4</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/man-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">5</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/man-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">6</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/man-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">7</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/woman-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">7</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/woman-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">7</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/woman-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">7</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/woman-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">7</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/woman-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">7</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/woman-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>
      <div class="item">
        <span class="num">7</span>
        <div class="info">
          <div class="logo">
            <img src="@/static/header/<EMAIL>" />
          </div>
          <div class="name">用户名</div>
          <img class="sex" src="@/static/live/woman-icon.png" />
        </div>
        <div class="hot">
          <img src="@/static/live/<EMAIL>" />32432423
        </div>
      </div>

    </div>
  </div>
</template>
<script setup lang="ts"></script>
<style scoped lang="scss">
.gift-rank-box {
  padding: 10px 3px 0 3px;
  box-sizing: border-box;
}

.top-box {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 2px;
  margin-bottom: 20px;

  .rank-top {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    padding: 10px 2px 10px;
    border-radius: 15px;
    background: white;
    box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    // min-width: 100px;

    // 第二名 (左侧)
    // &:nth-child(1) {
    //   order: 1;
    //   margin-top: 30px;

    //   .badge {
    //     width: 24px;
    //     height: 24px;
    //   }
    // }

    // 第一名 (中间，最高)
    &:nth-child(2) {
      height: 160px;
      // background: linear-gradient(135deg, #FFD700, #FFA500);
      margin-top: 0;

      .badge {
        width: 28px;
        height: 28px;
      }

      // .info {
      //   color: white;
      //   font-weight: bold;
      // }

      // .hot {
      //   color: white;
      // }
    }

    // 第三名 (右侧)
    // &:nth-child(3) {
    //   order: 3;
    //   margin-top: 30px;

    //   .badge {
    //     width: 24px;
    //     height: 24px;
    //   }
    // }

    .logo {
      position: relative;
      margin-bottom: 10px;

      img:first-child {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .badge {
        position: absolute;
        bottom: -15px;
        right: -13px;
        transform: translateX(-50%);
        width: 44px;
        height: 27px;
      }
    }

    .info {
      display: flex;
      align-items: center;
      gap: 5px;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      margin-top: 10px;
      margin-bottom: 2px;

      .sex {
        width: 16px;
        height: 16px;
      }
    }

    .hot {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #ff4757;
      font-weight: bold;

      img {
        width: 14px;
        height: 14px;
      }
    }
  }
}

.list-box {
  .item {
    display: flex;
    align-items: center;
    padding: 8px;
    background: white;
    border-bottom: 1px solid #D9D9D9;

    .num {
      font-size: 16px;
      font-weight: bold;
      color: #000;
      min-width: 30px;
      text-align: center;
      margin-right: 15px;
    }

    .info {
      display: flex;
      align-items: center;
      gap: 10px;
      flex: 1;

      .logo {
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          object-fit: cover;
        }
      }

      .name {
        font-size: 14px;
        font-weight: 400;
        color: #000;
      }

      .sex {
        width: 15px;
        height: 15px;
      }
    }

    .hot {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 12px;
      color: #FB2B1F;

      img {
        width: 10px;
        height: 12px;
      }
    }
  }
}
</style>