<template>
  <div class="schedule-center-wrapper">
    <div class="schedule-nav-bar">
      <div class="content">
        <div class="li" v-for="item in gameTypes" :key="item.value" :class="{ active: selectedGame === item.value }"
          @click="handleChangeCategory(item.value)">
          <span>{{ item.label }}</span>
        </div>
      </div>
    </div>
    <div class="schedule-date-nav-bar">
      <div class="content">
        <div class="date-item" :class="{ active: selectedDate === date.value }" v-for="date in dateList"
          :key="date.value" @click="selectDate(date.value)">
          <div class="date-label">{{ date.label }}</div>
          <div class="date-value">{{ date.value }}</div>
        </div>
      </div>
    </div>
    <div class="schedule-center" v-if="appointmentList.length > 0">
      <div class="appointment-list">
        <div class="appointment-item" v-for="item in appointmentList" :key="item.id">
          <div class="time-info">
            <div class="league">{{ item.sclassName }}</div>
            <div class="time">{{ dayjs(item.matchTime).format('HH:mm') }}</div>
          </div>
          <div class="match-info">
            <div class="team">
              <img :src="item.homeLogo" :alt="item.homeName" class="team-logo">
              <span class="team-name">{{ item.homeName }}</span>
            </div>
            <div class="team">
              <img :src="item.awayLogo" :alt="item.awayName" class="team-logo">
              <span class="team-name">{{ item.awayName }}</span>
            </div>
          </div>
          <div class="score-box" v-if="item.matchState === 2">
            <p>{{ item.homeScore }}</p>
            <p>{{ item.awayScore }}</p>
          </div>
          <div class="reserve-info" v-else>
            <img v-if="!item?.collectId" src="@/static/schedule/live-icon.png" class="reserve-icon"
              @click="handleReserve(0, item.id)">
            <img v-else src="@/static/schedule/live-icon-active.png" class="reserve-icon"
              @click="handleReserve(1, item.id)">
          </div>
          <div class="commentators">
            <div class="commentator" v-for="commentator in item.collectMatchDetailResList" :key="commentator.id"
              @click="handleToRoom(item.id, commentator.userId, item)">
              <img :src="commentator.userImage" :alt="commentator.userName" class="commentator-avatar">
              <div class="tip" v-if="commentator.liveState === 1">LIVE</div>
              <span class="commentator-name">{{ commentator.userName }}</span>
            </div>
          </div>
          <div class="status-section">
            <span class="status-text">{{ formatLiveState(item.liveState) }}</span>
          </div>
          <div class="red-line"></div>
        </div>
      </div>
    </div>
    <div v-else class="none-box">
      <none-box text="暂无赛程信息" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import noneBox from '@/components/module/none-box/index.vue'
import { getCourseApi, collectMatchApi, cancelCollectMatchApi } from '@/api/schedule'

const router = useRouter()
const message = useMessage()

interface GameType {
  label: string
  value: string
}

interface DateItem {
  label: string
  value: string
  apiDate: string
}

const gameTypes: GameType[] = [
  { label: '足球', value: 'foot' },
  { label: '篮球', value: 'basket' },
  { label: '英雄联盟', value: 'lol' },
  { label: 'DOTA2', value: 'dota' },
  { label: 'CS:GO', value: 'csgo' },
  { label: '王者荣耀', value: 'hok' }
]

// 动态生成日期列表
const generateDateList = () => {
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const today = dayjs()
  const dateList: DateItem[] = []
  for (let i = 0; i < 7; i++) {
    const currentDate = today.add(i, 'day')
    dateList.push({
      label: i === 0 ? '今天' : weekdays[currentDate.day()], // 今天/周几
      value: currentDate.format('MM.DD'), // 格式化为 月.日
      apiDate: currentDate.format('YYYY-MM-DD') // 接口用 年月日
    })
  }
  return dateList
}
// 初始化日期列表和默认选中日期
const dateList = ref<DateItem[]>(generateDateList())
const selectedDate = ref<string>(dateList.value[0].value)

const selectedGame = ref<string>('foot')
const selectDate = (date: string) => {
  selectedDate.value = date
  getCourseList()
}

const handleChangeCategory = (value: string) => {
  selectedGame.value = value
  getCourseList()
}

const formatLiveState = (value: number) => {
  switch (value) {
    case 0:
      return '未开播'
    case 1:
      return '直播中'
    default:
      break
  }
}

// 跳转直播间
interface matchDetailResItem {
  id: number;
  liveState: number;
  userId: number;
  userName: string;
  userImage: string;
  matchDetailRes: {
    matchId: number;
    liveTypeEnum: string;
    userId: number;
  }
}
const handleToRoom = (matchId: number, userId: number, item: matchDetailResItem) => {
  let url = '';
  if (item.matchDetailRes) {
    url = `/room?matchId=${item.matchDetailRes.matchId}&userId=${item.matchDetailRes.userId}&liveTypeEnum=${item.matchDetailRes.liveTypeEnum}`;
  } else {
    url = `/room?matchId=${matchId}&userId=${userId}&liveTypeEnum=${selectedGame.value}`;
  }
  router.push(url)
}

// 预约、取消预约比赛
const handleReserve = async (type: number, matchId: number) => {
  let data = {
    matchId,
    liveType: selectedGame.value
  }
  type ? await cancelCollectMatchApi(data) : await collectMatchApi(data)
  // 显示消息前先清除所有
  message.destroyAll()
  type ? message.success('取消预约成功') : message.success('预约成功')
  await getCourseList()
}

const appointmentList = ref<any[]>([])
// 获取赛程列表
const getCourseList = async () => {
  const selectedApiDate = dateList.value.find(date => date.value === selectedDate.value)?.apiDate
  if (!selectedApiDate) return
  const { data } = await getCourseApi({
    date: selectedApiDate,
    liveTypeEnum: selectedGame.value
  })
  appointmentList.value = data
}

onMounted(() => {
  getCourseList()
})
</script>

<style lang="scss" scoped>
.schedule-center-wrapper {
  width: 100%;
  background: #f5f5f5;
}

.schedule-nav-bar {
  background-color: #333333;
  height: 70px;
  width: 100%;

  .content {
    width: 1200px;
    height: 100%;
    display: flex;
    margin: 0 auto;

    .li {
      height: 100%;
      color: #fff;
      margin-right: 30px;
      font-size: 18px;
      display: flex;
      align-items: center;
      box-sizing: border-box;

      &.active,
      &:hover {
        span {
          border-bottom: 2px solid #FB2B1F;
        }
      }

      span {
        cursor: pointer;
        padding: 10px 0;
        border-bottom: 2px solid #333;
      }
    }
  }
}

.schedule-date-nav-bar {
  height: 70px;
  background-color: #fff;
  padding: 10px 0;
  box-sizing: content-box;

  .content {
    width: 1200px;
    height: 100%;
    display: flex;
    margin: 0 auto;

    .date-item {
      cursor: pointer;
      transition: all 0.3s ease;
      background: #fff;
      text-align: center;
      min-width: 90px;
      color: #333;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-radius: 8px;
      margin-right: 10px;

      &:hover {
        background: #FB2B1F;
        color: #fff;
      }

      &.active {
        background: #FB2B1F;
        color: #fff;

        .date-value {
          font-weight: bold;
        }

        .date-value {
          font-weight: bold;
        }
      }

      .date-label {
        font-size: 18px;
        margin-bottom: 4px;
        font-weight: 500;
      }

      .date-value {
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}


.schedule-center {
  width: 1200px;
  margin: 0 auto;
  padding: 40px 0;
  min-height: 600px;

  .appointment-list {
    .appointment-item {
      position: relative;
      display: flex;
      align-items: center;
      padding: 20px 24px;
      margin-bottom: 16px;
      background: #fff;
      overflow: hidden;
      border-radius: 8px;

      .time-info {
        width: 160px;
        margin-right: 32px;
        text-align: center;

        .league {
          font-size: 18px;
          color: #333;
          margin-bottom: 4px;
        }

        .time {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .match-info {
        display: flex;
        width: 240px;
        flex-direction: column;
        margin-right: 40px;
        gap: 16px;


        .team {
          display: flex;
          align-items: center;


          .team-logo {
            width: 32px;
            height: 32px;
            margin-right: 8px;
            border-radius: 50%;
          }

          .team-name {
            font-size: 18px;
            color: #333;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
        }

      }

      .reserve-info {
        width: 100px;

        .reserve-icon {
          width: 26px;
          height: 26px;
          margin-right: 4px;
          color: #333;
          cursor: pointer;
        }
      }

      .score-box {
        width: 100px;
        display: flex;
        flex-direction: column;
        font-size: 18px;
        color: #FF2F2E;
        gap: 10px;
      }

      .commentators {
        display: flex;
        align-items: center;
        flex: 1;
        gap: 20px;


        .commentator {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          cursor: pointer;
          transition: transform ease-in 0.3s;

          &:hover {
            transform: translateY(-4px);
          }

          .tip {
            position: absolute;
            bottom: 22px;
            right: 50%;
            transform: translateX(50%);
            background: #FB2B1F;
            color: #fff;
            font-size: 10px;
            padding: 2px 6px;
            line-height: 1;
            border-radius: 2px;
          }

          .commentator-avatar {
            width: 46px;
            height: 46px;
            border-radius: 50%;
            margin-right: 4px;
          }

          .commentator-name {
            color: #333;
            font-size: 14px;
            border-radius: 2px;
            white-space: nowrap;
            margin-top: 6px;
          }
        }
      }

      .status-section {
        margin-left: auto;
        margin-right: 16px;

        .status-text {
          font-size: 14px;
          color: #333;
        }
      }

      .red-line {
        position: absolute;
        right: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: #FB2B1F;
        border-radius: 0 8px 8px 0;
      }
    }
  }
}

.main-content {
  flex: 1;
  padding: 0 40px;
}

.none-box {
  // background-color: #fff;
  height: 472px;
}
</style>