<script setup lang="ts">
</script>

<template>
  <HeaderView></HeaderView>
  <div class="body-container">
    <RouterView v-slot="{ Component }">
      <Transition name="fade-transform" mode="out-in" appear>
        <!-- <keep-alive> -->
        <component :is="Component"></component>
        <!-- </keep-alive> -->
      </Transition>
    </RouterView>
  </div>
  <FooterView></FooterView>
</template>
<style lang="scss" scoped>
// .body-container {}</style>
