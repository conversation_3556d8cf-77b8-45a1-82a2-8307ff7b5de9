<template>
  <div class="gift-rank-view">
    <div class="tabs">
      <div v-for="tab in tabs" :key="tab.value" class="tabs-item" :class="{ active: activeTab === tab.value }"
        @click="handleChangeTab(tab.value)">
        {{ tab.label }}
      </div>
    </div>
    <component :is="RankView" class="chap" />
  </div>
</template>
<script setup lang="ts">
import RankView from './RankView.vue'

type Tab = {
  label: string
  value: string
}
const tabs: Tab[] = [
  { label: '人气排行榜', value: 'chap1' },
  { label: '米粒排行榜', value: 'chap2' },
  { label: '贡献排行榜', value: 'chap3' }
]
const activeTab = ref<string>('chap1')
const handleChangeTab = (value: string) => {
  activeTab.value = value
}
</script>
<style scoped lang="scss">
.gift-rank-view {
  background: #F4F4F4;
  overflow: hidden;

  .tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    padding: 0 20px;
    margin: 8px 0;
    font-size: 16px;
    color: #333;
    cursor: pointer;

    .tabs-item {
      transition: all .3s ease;
      padding: 2px 5px;
      font-size: 14px;
      border-radius: 2px;

      &:hover:not(.active) {
        color: #FB2B1F;
      }
    }

    .active {
      background-color: #FB2B1F;
      color: #FFF;
    }
  }

  .chap {
    overflow-y: auto;
    height: calc(100% - 46px);
  }
}
</style>