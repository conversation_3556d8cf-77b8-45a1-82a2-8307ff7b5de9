import request from '@/request/request'

interface MatchIdType {
  matchId: number;
}
// 本场统计数据
export const getMatchStatApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/foot/getStat.e', { params })
}

// 比赛事件
export const getIncidentsApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/foot/getIncidents.e', { params })
}

// 直播详情
interface LiveDetailsType {
  matchId: number;
  userId: number;
  liveTypeEnum: string;
}
export const getLiveDetailsApi = (params: LiveDetailsType) => {
  return request.get<any>('/live-customer/foot/getLiveDetails.e', { params })
}
//获取比方数据
interface ScoreDetailsType {
  matchId: number;
  liveTypeEnum: string;
}
export const getScoreDetailsApi = (params: ScoreDetailsType) => {
  return request.get<any>('/live-customer/foot/getDetails.e', { params })
}

// 首发阵容
export const getLineupApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/foot/getLineup.e', { params })
}

// 伤停情况 
export const getInjuryApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/foot/getInjury.e', { params })
}

// 进球分布
export const getDistributionApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/foot/getDistribution.e', { params })
}

// 积分排名
export const getTableApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/foot/getTable.e', { params })
}

// 历史交锋
interface HistoryType {
  matchId: number;
  type: number;
}
export const getHistoryApi = (params: HistoryType) => {
  return request.get<any>('/live-customer/foot/getHistory.e', { params })
}
export const getPlayerStatApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/basket/getStat.e', { params })
}

// 获取篮球球员数据统计
export const getPlayerApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/basket/getPlayer.e', { params })
}

// 获取篮球进球分布
export const getBestStatApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/basket/getBestStat.e', { params })
}

// 获取篮球历史交锋
export const getBasketHistoryApi = (params: HistoryType) => {
  return request.get<any>('/live-customer/basket/getHistory.e', { params })
}

// 获取篮球场均数据
export const getBasketAvgApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/basket/getStatAvg.e', { params })
}

// 获取篮球积分排名
export const getBasketTableApi = (params: MatchIdType) => {
  return request.get<any>('/live-customer/basket/getTable.e', { params })
}

// 通过房间id 查询最近10条消息
interface RoomIdType {
  roomId: number;
}
export const findByRoomIdApi = (params: RoomIdType) => {
  return request.get<any>('/live-customer/im/findByRoomId', { params })
}
