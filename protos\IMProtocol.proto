syntax = "proto3";

import "common.proto";
import "auth.proto";
import "chat.proto";
import "live.proto";

option java_package = "com.letu.solutions.model.netty.v2.protobuf";
option java_outer_classname = "IMProtocol";

// 顶层消息体
message IMMessage {
  Header header = 1;

  oneof body {
    auth.LoginRequest login_request = 1001;         //登录请求
    auth.LoginResponse login_response = 1002;       //登录响应
    auth.LogoutRequest logout_request = 1003;       //登出请求
    auth.LogoutResponse logout_response = 1004;     //退出响应
    chat.ChatMessage chat_message = 2001;           //聊天消息
    chat.MessageAck message_ack = 2002;             //消息回执
    chat.HistoryMessages history_messages = 2003;   //历史消息
    live.EnterLiveRoomEvent enter_live_room_event = 3001;  //进入直播间
    live.ExitLiveRoomEvent exit_live_room_event = 3002;   //退出直播间
    string heartbeat = 6;
  }
}

// 消息头结构
message Header {
  string message_id = 1;  //消息id
  int64 timestamp = 2;    //时间戳
  int32 version = 3;      //版本
}
