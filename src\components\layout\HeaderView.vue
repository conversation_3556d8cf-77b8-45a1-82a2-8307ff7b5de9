<template>
  <header class="header">
    <div class="header-container">
      <!-- Logo区域 -->
      <div class="logo-section">
        <img class="icon" src="@/static/header/<EMAIL>" />
      </div>

      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <a href="/" class="nav-item" :class="{ active: currentRoute === '/home' }">首页</a>
        <a href="/all" class="nav-item" :class="{ active: currentRoute === '/all' }">全部直播</a>
        <a href="/schedule" class="nav-item" :class="{ active: currentRoute === '/schedule' }">赛程</a>
      </nav>

      <!-- 右侧功能区 -->
      <div class="header-actions">
        <!-- 下载APP -->
        <div class="download-app" @mouseenter="showQRCode = true" @mouseleave="showQRCode = false">
          <span>下载APP</span>
          <div v-show="showQRCode" class="qr-code-popup">
            <div class="qr-placeholder"></div>
            <p>扫码下载APP</p>
          </div>
        </div>

        <!-- 搜索框 -->
        <!-- <div class="search-box">
          <input type="text" placeholder="搜索" v-model="searchQuery" />
          <div class="search-icon"></div>
        </div> -->

        <!-- 登录注册 -->
        <div class="auth-buttons">
          <template v-if="!isLogin">
            <div class="btn login-btn" @click="showLoginModal">登录</div>
            <div class="btn register-btn" @click="showRegisterModal">注册</div>
          </template>
          <template v-else>
            <div class="btn yy-btn" @click="goToMatchFollow"><img class="icon" src="@/static/header/<EMAIL>" />
              预约</div>
            <div class="btn gz-btn" @click="goToAnchorFollow"><img class="icon" src="@/static/header/<EMAIL>" />
              关注</div>
            <div class="logo">
              <n-popover trigger="hover" placement="bottom" :show-arrow="true"
                :content-style="{ padding: '0', width: '500px' }">
                <template #trigger>
                  <img class="icon" :src="userInfo.userImage" alt="用户头像" />
                </template>
                <div class="user-popup">
                  <!-- 用户信息区域 -->
                  <div class="user-info">
                    <div class="user-avatar">
                      <img :src="userInfo.userImage" alt="用户头像" />
                    </div>
                    <div class="user-details">
                      <div class="username">{{ userInfo.userName }}</div>
                      <n-popconfirm @positive-click="handleLogout" positive-text="确认" negative-text="取消">
                        <template #trigger>
                          <button class="logout-btn">退出登录</button>
                        </template>
                        确定要退出登录吗？
                      </n-popconfirm>
                    </div>
                  </div>

                  <!-- 等级进度条 -->
                  <div class="level-progress">
                    <div class="level-info">
                      <span class="level-badge lv1">LV1</span>
                      <span class="experience">经验值 1/100</span>
                      <span class="level-badge lv2">LV2</span>
                    </div>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: '20%' }"></div>
                    </div>
                  </div>

                  <!-- 功能按钮区域 -->
                  <div class="action-buttons">
                    <div class="action-item" @click="goToPersonalCenter">
                      <div class="action-icon">
                        <img src="@/static/header/<EMAIL>" />
                      </div>
                      <span class="action-text">个人中心</span>
                    </div>
                    <div class="action-item" @click="goToAnchorFollow">
                      <div class="action-icon">
                        <img src="@/static/header/<EMAIL>" />
                      </div>
                      <span class="action-text">主播关注</span>
                    </div>
                    <div class="action-item" @click="goToMatchFollow">
                      <div class="action-icon">
                        <img src="@/static/header/<EMAIL>" />
                      </div>
                      <span class="action-text">比赛关注</span>
                    </div>
                  </div>
                </div>
              </n-popover>
            </div>
          </template>
        </div>
      </div>
    </div>
  </header>

  <!-- 登录注册弹窗 -->
  <AuthModal v-model:show="showAuthModal" :type="authModalType" />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useMessage } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import AuthModal from '@/components/auth/AuthModal.vue'

const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)
const message = useMessage()
const route = useRoute()
const router = useRouter()
const isLogin = computed(() => userStore.isLogin)
console.log('isLogin', isLogin.value)
const showQRCode = ref(false)

// 获取当前路由路径
const currentRoute = computed(() => route.path)

// 认证弹窗相关
const showAuthModal = ref(false)
const authModalType = ref<'login' | 'register'>('login')

// 显示登录弹窗
const showLoginModal = () => {
  authModalType.value = 'login'
  showAuthModal.value = true
}

// 显示注册弹窗
const showRegisterModal = () => {
  authModalType.value = 'register'
  showAuthModal.value = true
}

// 处理退出登录
const handleLogout = async () => {
  try {
    // 这里可以调用用户store的退出登录方法
    await userStore.loginOut()
    // router.push('/')
    router.replace('/')
    message.success('退出登录成功')
  } catch (error) {
    message.error('退出登录失败')
  }
}

// 跳转到个人中心
const goToPersonalCenter = () => {
  router.push('/user')
}

// 跳转主播关注
const goToAnchorFollow = () => {
  router.push('/user/follows')
}

// 跳转比赛关注
const goToMatchFollow = () => {
  router.push('/user/appointments')
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 80px;
  background: #ffffff;
  // border-bottom: 1px solid var(--color-gray-200);
  position: sticky;
  top: 0;
  z-index: 1000;

  .header-container {
    width: 1200px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    // padding: 0 20px;
  }

  .logo-section {
    .icon {
      display: block;
      width: 172px;
      height: 42px;
    }
  }



  .nav-menu {
    display: flex;
    gap: 40px;
    margin-left: 36px;

    .nav-item {
      color: #333333;
      text-decoration: none;
      font-size: 20px;
      padding: 6px 20px;
      border-radius: 4px;
      transition: all var(--duration-200);
      position: relative;

      &:hover {
        color: #ffffff;
        background: #FB2B1F;
      }

      &.active {
        font-weight: 600;
        color: #ffffff;
        background: #FB2B1F;
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-left: auto;

    .download-app {
      position: relative;
      color: #FB2B1F;
      font-size: 20px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 4px;
      transition: all var(--duration-200);

      &:hover {
        color: var(--index);
        background: var(--color-transparent-index-100);
      }

      .qr-code-popup {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: #ffffff;
        border: 1px solid var(--color-gray-200);
        border-radius: 8px;
        padding: 16px;
        box-shadow: var(--boxshadow-medium);
        z-index: 1001;
        margin-top: 8px;

        &::before {
          content: '';
          position: absolute;
          top: -6px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-bottom: 6px solid #ffffff;
        }

        .qr-placeholder {
          width: 120px;
          height: 120px;
          background: var(--color-gray-200);
          border-radius: 4px;
          margin-bottom: 8px;
        }

        p {
          margin: 0;
          font-size: var(--font-size-75);
          color: var(--color-gray-600);
          text-align: center;
        }
      }
    }

    .search-box {
      position: relative;
      display: flex;
      align-items: center;

      input {
        width: 200px;
        height: 36px;
        background: #F4F4F4;
        border: none;
        border-radius: 18px;
        padding: 0 40px 0 16px;
        font-size: var(--font-size-100);
        color: var(--color-gray-800);
        outline: none;
        transition: all var(--duration-200);

        &::placeholder {
          color: var(--color-gray-500);
        }

        &:focus {
          background: #ffffff;
          border: 1px solid var(--index);
        }
      }

      .search-icon {
        position: absolute;
        right: 12px;
        width: 16px;
        height: 16px;
        background: var(--color-gray-500);
        border-radius: 2px;
        cursor: pointer;
      }
    }

    .auth-buttons {
      display: flex;
      gap: 30px;


      .login-btn {
        width: 80px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ffffff;
        color: #FB2B1F;
        border: 1px solid #FB2B1F;


      }

      .register-btn {
        width: 80px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #FB2B1F;
        color: #ffffff;
      }

      .btn {
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
        box-sizing: border-box;

        .icon {
          width: 26px;
          height: 26px;
          display: block;
          margin-right: 2px;
        }
      }

      .yy-btn {
        font-size: 14px;
        color: #333333;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .gz-btn {
        font-size: 14px;
        color: #333333;
        display: flex;
        align-items: center;
      }

      .logo {
        cursor: pointer;

        .icon {
          width: 44px;
          height: 44px;
          display: block;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>

<style lang="scss">
// 用户弹出框全局样式（因为 n-popover 渲染到 body 下）
.user-popup {
  width: 500px !important;
  background: #ffffff;
  padding: 20px;

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .user-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 16px;
      background: #f0f0f0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .user-details {
      flex: 1;
      display: flex;
      justify-content: space-between;

      .username {
        font-size: 24px;
        font-weight: 600;
        color: #000;
      }

      .logout-btn {
        background: none;
        border: 1px solid #e0e0e0;
        color: #666666;
        font-size: 14px;
        padding: 4px 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          border-color: #FB2B1F;
          color: #FB2B1F;
        }
      }
    }
  }

  .level-progress {
    margin-bottom: 24px;

    .level-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .level-badge {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: 600;

        &.lv1 {
          background: #e0e0e0;
          color: #666666;
        }

        &.lv2 {
          background: #4CAF50;
          color: #ffffff;
        }
      }

      .experience {
        font-size: 14px;
        color: #666666;
      }
    }

    .progress-bar {
      width: 100%;
      height: 6px;
      background: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #FB2B1F 0%, #FF6B5B 100%);
        border-radius: 3px;
        transition: width 0.3s ease;
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: space-around;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      padding: 12px;
      border-radius: 8px;
      transition: all 0.2s;

      .action-icon {

        img {
          width: 32px;
          height: 32px;
          display: block;
        }
      }

      .action-text {
        font-size: 14px;
        color: #000;
        margin-top: 2px;
      }

      &:hover {
        background-color: #e2e2e2;
      }

    }
  }
}
</style>