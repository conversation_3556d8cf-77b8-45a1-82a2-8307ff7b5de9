<template>
  <div class="user-profile">
    <div class="profile-header">
      <div class="line"></div>
      <h3>我的资料</h3>
    </div>

    <div class="profile-form">
      <div class="form-content">
        <div class="form-row one">
          <label class="form-label">昵称</label>
          <div class="form-input">
            <n-input v-model:value="profileData.name" placeholder="请输入昵称" class="input-field" />
          </div>
        </div>
        <div class="sub-content">
          <div class="form-row two">
            <label class="form-label">性别</label>
            <div class="form-input">
              <n-select v-model:value="profileData.sex" :options="genderOptions" placeholder="请选择"
                class="select-field" />
            </div>
          </div>

          <div class="form-row two">
            <label class="form-label">生日</label>
            <div class="form-input">
              <n-date-picker v-model:value="profileData.birthday" type="date" placeholder="请选择日期" class="date-field"
                :is-date-disabled="disableFutureDates" />
            </div>
          </div>
        </div>

        <div class="form-actions">
          <n-button type="primary" size="large" class="save-btn" @click="handleSave">
            保存资料
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useMessage } from 'naive-ui'
import { useUserStore } from '@/stores/user'
import { updateUserNameApi, updateUserSexApi, updateUserBirthdayApi } from '@/api/user'
import dayjs from 'dayjs'

const message = useMessage()
const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)

// 表单数据
const profileData = ref({
  name: userInfo.value.userName || '',
  sex: userInfo.value.sex || '',
  birthday: dayjs(userInfo.value.birthday, 'YYYYMMDD').valueOf() || null
})

// 性别选项
const genderOptions = [
  { label: '男', value: 'boy' },
  { label: '女', value: 'girl' },
  { label: '保密', value: 'unknown' }
]

// 禁用今天之后的日期
const disableFutureDates = (currentDate: number) => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return currentDate > today.getTime()
}

// 保存资料
const handleSave = async () => {
  if (profileData.value.name === '') {
    message.warning('昵称不能为空')
    return
  }
  if (profileData.value.birthday === null) {
    message.warning('生日不能为空')
    return
  }

  try {
    // const [nameRes, sexRes, birthdayRes] = 
    await Promise.all([
      updateUserNameApi({ name: profileData.value.name }),
      updateUserSexApi({ sex: profileData.value.sex }),
      updateUserBirthdayApi({ birthday: dayjs(profileData.value.birthday).format('YYYYMMDD') })
    ])

    // 检查三个接口是否都返回成功
    // if (nameRes.code === 0 && sexRes.code === 0 && birthdayRes.code === 0) {
    message.success('资料保存成功')
    // 更新store中的用户信息
    userStore.updateUserInfo()
    // } else {
    //   // 如果有接口失败，提示用户
    //   message.error('资料保存失败，请重试')
    // }
  } catch (error) {
    console.error('保存资料出错:', error)
    message.error('资料保存失败，请重试')
  }
}
</script>

<style lang="scss" scoped>
.user-profile {
  background-color: #fff;
  min-height: 100%;

  .profile-header {
    padding: 32px 0 40px 36px;
    display: flex;
    align-items: center;

    .line {
      width: 3px;
      height: 16px;
      margin-right: 8px;
      background: #FB2B1F;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .profile-form {
    display: flex;
    justify-content: center;

    .form-content {
      width: 600px;

      .sub-content {
        display: flex;
        justify-content: space-between;
      }
    }

    .form-row {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: 24px;

      &.two {
        width: 46%;
      }

      .form-label {
        width: 80px;
        font-size: 14px;
        color: #BABABA;
        margin-bottom: 10px;
      }

      .form-input {
        flex: 1;
        // max-width: 300px;

        .input-field,
        .select-field,
        .date-field {
          width: 100%;
        }
      }
    }

    .form-actions {
      margin-top: 40px;

      .save-btn {
        width: 200px;
        height: 40px;
        background: #FB2B1F !important;
        border-color: #FB2B1F !important;

        &:hover {
          background: #e02419 !important;
          border-color: #e02419 !important;
        }
      }
    }
  }
}
</style>
