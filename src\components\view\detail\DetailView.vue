<template>
  <div class="detail-page">
    <div class="live-container">
      <div class="live-content">
        <div class="video-box">
          <div class="header">
            <div class="logo-box">
              <img class="logo" :src="liveDetail.userImage ? liveDetail.userImage : ''" />
            </div>
            <div class="info-box">
              <p class="title" v-if="liveTypeEnum === 'basket'">
                {{ liveDetail.sclassName }} {{ liveDetail.awayName }} VS {{ liveDetail.homeName }}
              </p>
              <p class="title" v-else>
                {{ liveDetail.liveTitle || '' }}
              </p>
              <div class="other">
                <div class="left">
                  <span class="name">{{ liveDetail.userName || '' }}</span>
                  <span class="number">房间号:{{ liveDetail.userId || '' }}</span>
                  <div class="viewer-count">
                    <img class="hot" src="@/static/live/<EMAIL>" />
                    {{ formatViewerCount(liveDetail.hotNum || 0) }}
                  </div>
                  <span class="load"><img class="icon" src="@/static/live/app-icon.png" />下载APP可投屏电视</span>
                </div>
                <div class="right">
                  <div class="share">

                  </div>
                  <div class="fllow-box">
                    <span class="num">{{ liveDetail.collectLiveMax || 0 }}</span>
                    <div class="text" :class="{ clear: liveDetail.liveCollectId }" @click="handleFollow">{{
                      liveDetail.liveCollectId === 1 ? '已关注' : '关注' }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="player-box" id="dplayer-warp">
            <div id="dplayer" v-if="liveDetail.liveState === 1"></div>
            <div class="cancel-muted" v-if="isMuted" @click="toggleMute">
              <img class="icon" src="@/static/live/cancel-muted-icon.png" /> <span class="t">点击取消静音</span>
            </div>
            <!-- 自定义控制栏 -->
            <!-- @mouseenter="handleMouseEnterControls" @mouseleave="handleMouseLeaveControls" -->
            <!-- :class="{ 'show': showControls, 'hide': !showControls }" -->
            <div class="custom-controls" v-if="liveDetail.liveState === 1">
              <!-- 控制按钮区域 -->
              <div class="controls-area">
                <!-- 左侧控制组 -->
                <div class="controls-left">
                  <!-- 播放/暂停按钮 -->
                  <div class="control-btn play-btn" @click="togglePlay">
                    <svg v-if="!isPlaying" viewBox="0 0 24 24" width="20" height="20">
                      <path d="M8 5v14l11-7z" fill="currentColor" />
                    </svg>
                    <svg v-else viewBox="0 0 24 24" width="20" height="20">
                      <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" fill="currentColor" />
                    </svg>
                  </div>

                  <!-- 音量控制 -->
                  <div class="volume-control" @mouseenter="showVolumeSlider = true"
                    @mouseleave="showVolumeSlider = false">
                    <div class="control-btn volume-btn" @click="toggleMute">
                      <svg v-if="!isMuted && volume > 0.5" viewBox="0 0 24 24" width="18" height="18">
                        <path
                          d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"
                          fill="currentColor" />
                      </svg>
                      <svg v-else-if="!isMuted && volume > 0" viewBox="0 0 24 24" width="18" height="18">
                        <path
                          d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"
                          fill="currentColor" />
                      </svg>
                      <svg v-else viewBox="0 0 24 24" width="18" height="18">
                        <path
                          d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"
                          fill="currentColor" />
                      </svg>
                    </div>
                    <!-- <transition name="volume-fade"> -->
                    <div class="volume-slider" v-show="showVolumeSlider">
                      <div class="volume-track" @click="handleVolumeClick" ref="volumeTrackRef">
                        <div class="volume-fill" :style="{ height: (isMuted ? 0 : volume * 100) + '%' }"></div>
                        <div class="volume-thumb" :style="{ bottom: (isMuted ? 0 : volume * 100) + '%' }"
                          @mousedown="handleVolumeDrag"></div>
                      </div>
                    </div>
                    <!-- </transition> -->
                  </div>
                </div>

                <!-- 右侧控制组 -->
                <div class="controls-right">
                  <!-- 清晰度切换 -->
                  <div class="definition-control" v-if="definitionList.length > 1">
                    <div class="control-btn definition-btn" @click="toggleDefinitionList"
                      :class="{ active: showDefinitionList }">
                      <span>{{ currentDefinition }}</span>
                    </div>
                    <transition name="definition-fade">
                      <div class="definition-list" v-show="showDefinitionList">
                        <div class="definition-item" v-for="item in definitionList" :key="item.definition" :class="{
                          selected: item.definition === currentDefinition,
                          premium: item.definition === '蓝光'
                        }" @click="handleDefinitionChange(item)">
                          <span class="definition-text">{{ item.text }}</span>
                          <span v-if="item.definition === '蓝光' && !isLogin" class="login-tag">登录即享</span>
                        </div>
                      </div>
                    </transition>
                  </div>

                  <!-- 样式全屏按钮 -->
                  <div class="control-btn fullscreen-btn" @click="toggleFullscreen">
                    <svg v-if="!isFullscreen" viewBox="0 0 24 24" width="24" height="24">
                      <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"
                        fill="currentColor" />
                    </svg>
                    <svg v-else viewBox="0 0 24 24" width="24" height="24">
                      <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"
                        fill="currentColor" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div class="live-end-warp" v-else>
              <p class="tip">主播不在家，可观看其他直播</p>
              <div class="sub-box">
                <LiveItem :live-data="item" v-for="item in homeLiveList" :key="item.matchId" />
              </div>
            </div>
          </div>
          <div class="footer-box" v-if="liveDetail.liveState === 1">
            <div class="left-box">
              <div class="live-badge">
                <img class="live-dot" src="@/static/live/live-icon.gif" />直播中
              </div>
              <span>{{ dayjs(liveDetail.matchTime).format('MM-DD HH:mm') }}</span>
              <!-- <span>07:22</span> -->
              <span>{{ liveDetail.sclassName }}</span>
            </div>
            <div class="right-box">
              <div class="sub-box" v-if="liveTypeEnum === 'basket'">
                <div class="team">
                  <span>{{ liveDetail.awayName }}</span>
                  <img class="logo" :src="liveDetail.awayLogo" />
                </div>
                <div class="score">
                  <span class="num" v-show="isShowScore">{{ scoreDetail.awayScore }}</span>
                  <span class="num">{{ isShowScore ? ':' : 'vs' }}</span>
                  <span class="num" v-show="isShowScore">{{ scoreDetail.homeScore }}</span>
                </div>
                <div class="team">
                  <img class="logo" :src="liveDetail.homeLogo" />
                  <span>{{ liveDetail.homeName }}</span>
                </div>
              </div>
              <div class="sub-box" v-else>
                <div class="team">
                  <span>{{ liveDetail.homeName }}</span>
                  <img class="logo" :src="liveDetail.homeLogo" />
                </div>
                <div class="score">
                  <span class="num" v-show="isShowScore">{{ scoreDetail.homeScore }}</span>
                  <span class="num">{{ isShowScore ? ':' : 'vs' }}</span>
                  <span class="num" v-show="isShowScore">{{ scoreDetail.awayScore }}</span>
                </div>
                <div class="team">
                  <img class="logo" :src="liveDetail.awayLogo" />
                  <span>{{ liveDetail.awayName }}</span>
                </div>
              </div>
              <div class="btn" @click="isShowScore = !isShowScore">{{ isShowScore ? '隐藏比分' : '显示比分' }}</div>
            </div>
          </div>
        </div>
        <div class="msg-box">
          <div v-if="liveDetail.liveNotice" class="notice">
            <span class="label">公告：</span>{{ liveDetail.liveNotice }}
          </div>
          <div class="tabs">
            <div v-for="tab in tabs" :key="tab.value" class="tabs-item" :class="{ active: activeTabs === tab.value }"
              @click="handleChangeTabs(tab.value)">
              {{ tab.label }}
            </div>
          </div>
          <div v-show="activeTabs === 'chat'" class="tab-content-wrap">
            <!-- <chat-view v-if="liveDetail.userId" :userId="liveDetail.userId" /> -->
          </div>
          <div v-show="activeTabs === 'rank'" class="tab-content-wrap">
            <gift-rank-view />
          </div>
          <!-- <component :userId="Number(liveDetail.userId) || 0" :is="activeTabs === 'chat' ? ChatView : GiftRankView" /> -->
        </div>
      </div>
    </div>
    <div class="data-container">
      <div class="base-data" v-if="liveTypeEnum === 'foot'">
        <div class="chart-box">
          <div class="chart-item">
            <div class="name">进攻</div>
            <div class="chart">
              <div class="number">{{ matchStat.homeAttack || 0 }}</div>
              <n-progress type="circle" :percentage="getPercentage(matchStat.awayAttack, matchStat.homeAttack)"
                class="chart-progress" :stroke-width="12" :gap-degree="0" :gap-offset-degree="180" :height="58"
                color="#3177FD" rail-color="#FB2B1F" :show-indicator="false" />
              <div class="number">{{ matchStat.awayAttack || 0 }}</div>
            </div>
            <div class="data">
              <div class="item">
                <details-icons type="CORNER"></details-icons><text class="number">{{ matchStat.homeCornerKick || 0
                }}</text>
              </div>
              <div class="item">
                <details-icons type="RED"></details-icons><text class="number">{{ matchStat.homeRedCard || 0 }}</text>
              </div>
              <div class="item">
                <details-icons type="YELLOW"></details-icons><text class="number">{{ matchStat.homeYellowCard || 0
                }}</text>
              </div>
            </div>
          </div>
          <div class="chart-item">
            <div class="name">危险进攻</div>
            <div class="chart">
              <div class="number">{{ matchStat.homeDangerAttack }}</div>
              <n-progress type="circle"
                :percentage="getPercentage(matchStat.awayDangerAttack, matchStat.homeDangerAttack)"
                class="chart-progress" :stroke-width="12" :gap-degree="0" :gap-offset-degree="180" :height="58"
                color="#3177FD" rail-color="#FB2B1F" :show-indicator="false" />
              <div class="number">{{ matchStat.awayDangerAttack }}</div>
            </div>
            <div class="text">
              角球/红黄牌
            </div>
          </div>
          <div class="chart-item">
            <div class="name">控球率</div>
            <div class="chart">
              <div class="number">{{ matchStat.homeBallControlRate }}</div>
              <n-progress type="circle"
                :percentage="getPercentage(matchStat.awayBallControlRate, matchStat.homeBallControlRate)"
                class="chart-progress" :stroke-width="12" :gap-degree="0" :gap-offset-degree="180" :height="58"
                color="#3177FD" rail-color="#FB2B1F" :show-indicator="false" />
              <div class="number">{{ matchStat.awayBallControlRate }}</div>
            </div>
            <div class="data">
              <div class="item">
                <text class="number">{{ matchStat.awayYellowCard }}</text><details-icons type="YELLOW"></details-icons>
              </div>
              <div class="item">
                <text class="number">{{ matchStat.awayRedCard }}</text><details-icons type="RED"></details-icons>
              </div>
              <div class="item">
                <text class="number">{{ matchStat.awayCornerKick }}</text><details-icons type="CORNER"></details-icons>
              </div>
            </div>
          </div>
        </div>
        <div class="shoot-box">
          <div class="shoot-number-box">
            <div class="number-data">
              <span class="home active">{{ matchStat.homeShootRight + matchStat.homeDeflection }}</span>
              <span class="text"> 射门次数</span>
              <span class="visiting ">{{ matchStat.awayShootRight + matchStat.awayDeflection }}</span>
            </div>
            <div class="shoot-off-box">
              <div class="off-data">
                <span class="home active">{{ matchStat.homeDeflection }}</span>
                <span class="text">射偏次数</span>
                <span class="visiting ">{{ matchStat.awayDeflection }}</span>
              </div>
              <div class="shoot-on-box">
                <div class="on-data">
                  <span class="home active">{{ matchStat.homeShootRight }}</span>
                  <span class="text">射正次数</span>
                  <span class="visiting">{{ matchStat.awayShootRight }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 暂无数据 -->
      <!-- <div v-if="liveDetail.liveState === 0 && liveTypeEnum === 'foot'" class="none-box">
        <none-box text="暂无比赛数据" />
      </div> -->
      <!-- <div class="basketball-data" v-if="liveTypeEnum === 'basket'">
        <div class="score-box">
          <div class="score-item">
            <div class="label">球队</div>
            <div class="data top">勇士</div>
            <div class="data top">湖人</div>
          </div>
          <div class="sub-score">
            <div class="score-item">
              <div class="label">第一节</div>
              <div class="data">20</div>
              <div class="data">20</div>
            </div>
            <div class="score-item">
              <div class="label">第二节</div>
              <div class="data">20</div>
              <div class="data">20</div>
            </div>
            <div class="score-item">
              <div class="label">第三节</div>
              <div class="data">20</div>
              <div class="data">20</div>
            </div>
            <div class="score-item">
              <div class="label">第四节</div>
              <div class="data">20</div>
              <div class="data">20</div>
            </div>
            <div class="score-item">
              <div class="label">总分</div>
              <div class="data">20</div>
              <div class="data">20</div>
            </div>
          </div>
        </div>
        <div class="stat-box">
          <div class="team">
            <img class="logo" :src="liveDetail.homeLogo" />
            <p class="name">{{ liveDetail.homeName }}</p>
          </div>
          <div class="content">
            <div class="item">
              <span class="number">1</span><span class="label">剩余暂停</span><span class="number">1</span>
            </div>
            <div class="item">
              <span class="number">1</span><span class="label">本节犯规</span><span class="number">1</span>
            </div>
          </div>
          <div class="team">
            <img class="logo" :src="liveDetail.awayLogo" />
            <p class="name">{{ liveDetail.awayName }}</p>
          </div>
        </div>
      </div> -->
      <div class="tab-content" v-if="liveTypeEnum === 'foot'">
        <n-tabs type="line" animated add-tab-class="tab-box">
          <n-tab-pane name="history" tab="比赛事件">
            <match-event :incidents="incidents"></match-event>
          </n-tab-pane>
          <n-tab-pane name="lineup" tab="首发阵容">
            <div class="line-up-box">
              <line-up :matchId="matchId" :homeLogo="liveDetail.homeLogo" :awayLogo="liveDetail.awayLogo"></line-up>
            </div>
          </n-tab-pane>
          <n-tab-pane name="data" tab="数据">
            <div class="line-up-box">
              <LiveDataView :matchId="matchId" :liveDetail="liveDetail" />
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
      <div class="tab-content" v-if="liveTypeEnum === 'basket'">
        <n-tabs type="line" animated add-tab-class="tab-box">
          <n-tab-pane name="result" tab="赛况">
            <!-- 判断比赛是否已经开始 -->
            <!-- <template v-if="isMatchStart"> -->
            <BasketballResult :matchId="matchId" :liveDetail="liveDetail" />
            <!-- </template> -->
          </n-tab-pane>
          <n-tab-pane name="data" tab="数据">
            <BasketballData :matchId="matchId" :liveDetail="scoreDetail" />
          </n-tab-pane>
        </n-tabs>
      </div>
      <div class="hot-section" v-if="liveTypeEnum !== 'foot' && liveTypeEnum !== 'basket'">
        <div class="public-title">
          <p class="title">正在热播</p>
        </div>
        <div class="list-content" v-if="hotLiveList.length > 0">
          <LiveItem :live-data="item" v-for="item in hotLiveList" :key="item.matchId" />
        </div>
        <div v-else class="none-box">
          <none-box text="暂无比赛直播" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { useRoute } from 'vue-router'
import Player from 'xgplayer';
import HlsPlugin from 'xgplayer-hls';
import 'xgplayer/dist/index.min.css';
import DetailsIcons from '@/components/module/data-icon/index.vue'
import MatchEvent from '@/components/module/event-line/index.vue'
import LineUp from '@/components/module/line-up/index.vue'
import LiveDataView from '@/components/module/live-data/LiveDataView.vue'
import BasketballResult from '@/components/module/bb-result/BasketballResult.vue';
import BasketballData from '@/components/module/bb-data/BasketballData.vue';
import ChatView from '@/components/module/chat/ChatView.vue'
import GiftRankView from '@/components/module/gift-rank/GiftRankView.vue'
import dayjs from 'dayjs'
import { getMatchStatApi, getIncidentsApi, getLiveDetailsApi, getScoreDetailsApi } from '@/api/match'
import { getLivePageApi } from '@/api/index'
import { followUserApi, cancelFollowUserApi } from '@/api/user'
import { useUserStore } from '@/stores/user'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth';
const authStore = useAuthStore()
type Tab = {
  label: string
  value: string
}
const tabs: Tab[] = [
  { label: '聊天室', value: 'chat' },
  { label: '排行榜', value: 'rank' }
]

// 清晰度配置接口
interface MatchLiveClearItem {
  createTime: string;
  id: number;
  liveName: string;
  liveType: string;
  liveUrl: string;
  matchId: number;
  screenshotUrl: string;
}
const route = useRoute()
const matchId = computed(() => {
  const id = parseInt(route.query.matchId as string)
  return isNaN(id) ? 0 : id
})
const userId = computed(() => {
  const id = parseInt(route.query.userId as string)
  return isNaN(id) ? 0 : id
})
const liveTypeEnum = computed(() => route.query.liveTypeEnum || 'foot')
const isShowScore = ref(true)

// 用户状态管理
const userStore = useUserStore()
const message = useMessage()
const isLogin = computed(() => userStore.isLogin)

// 自定义清晰度切换相关
const definitionList = ref<Array<{ definition: string, url: string, text: string }>>([])
const currentDefinition = ref('高清')
const showDefinitionList = ref(false)

// 自定义控制栏相关
// const showControls = ref(false)
const isPlaying = ref(false)
const volume = ref(0.6)
const isMuted = ref(false)
const showVolumeSlider = ref(false)
const isFullscreen = ref(false)

// 控制栏自动隐藏相关
// const controlsTimer = ref<NodeJS.Timeout | null>(null)

const activeTabs = ref<string>('chat')
const handleChangeTabs = (value: string) => {
  activeTabs.value = value
}

// DOM 引用
const volumeTrackRef = ref<HTMLElement>()
const formatViewerCount = (count: number): string => {
  // 确保 count 是一个有效的数字
  const validCount = typeof count === 'number' && !isNaN(count) ? count : 0

  if (validCount >= 10000) {
    return `${(validCount / 10000).toFixed(1)}万`
  } else if (validCount >= 1000) {
    return `${(validCount / 1000).toFixed(1)}k`
  }
  return validCount.toString()
}


const getPercentage = (a = 0, b = 0) => {
  // 确保参数是有效的数字
  const validA = typeof a === 'number' && !isNaN(a) ? a : 0;
  const validB = typeof b === 'number' && !isNaN(b) ? b : 0;
  const total = validA + validB;
  return total === 0 ? 0 : (validA / total) * 100;
}
// 播放器实例和 HLS 实例的引用
let player: any = null;
let hlsInstance: any = null;

// 检查清晰度是否需要登录
const checkQualityPermission = (qualityName: string) => {
  // 蓝光需要登录
  if (qualityName === '蓝光' && !isLogin.value) {
    authStore.showLogin()
    return false
  }
  return true
}

// 自定义清晰度切换方法
const toggleDefinitionList = () => {
  showDefinitionList.value = !showDefinitionList.value
}

// 处理清晰度切换
const handleDefinitionChange = (item: { definition: string, url: string, text: string }) => {
  // 检查权限
  if (!checkQualityPermission(item.definition)) {
    return
  }
  // 更新当前清晰度
  currentDefinition.value = item.definition
  showDefinitionList.value = false

  // 切换播放器URL
  if (player) {
    try {
      // 直接切换播放源
      // player.src = item.url
      player.switchURL(item.url)
      console.log('切换清晰度:', item.definition, item.url)
    } catch (error) {
      console.error('切换清晰度失败:', error)
      message.error('切换清晰度失败，请重试')
      return
    }
  }

  message.success(`已切换到${item.definition}画质`)
}

// 点击外部关闭清晰度列表
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const selector = document.querySelector('.custom-definition-selector')
  if (selector && !selector.contains(target)) {
    showDefinitionList.value = false
  }
}

// 播放/暂停控制
const togglePlay = () => {
  if (!player) return

  if (isPlaying.value) {
    player.pause()
  } else {
    player.play()
  }
}

// 音量控制
const toggleMute = () => {
  if (!player) return

  if (isMuted.value) {
    player.muted = false
    isMuted.value = false
  } else {
    player.muted = true
    isMuted.value = true
  }
}

// 处理音量点击
const handleVolumeClick = (event: MouseEvent) => {
  if (!volumeTrackRef.value || !player) return

  const rect = volumeTrackRef.value.getBoundingClientRect()
  const y = event.clientY - rect.top
  const height = rect.height
  const newVolume = Math.max(0, Math.min(1, 1 - (y / height)))

  volume.value = newVolume
  player.volume = newVolume

  if (newVolume > 0) {
    isMuted.value = false
    player.muted = false
  }
}

// 处理音量拖拽
const handleVolumeDrag = (event: MouseEvent) => {
  event.preventDefault()

  const handleMouseMove = (e: MouseEvent) => {
    if (!volumeTrackRef.value || !player) return

    const rect = volumeTrackRef.value.getBoundingClientRect()
    const y = e.clientY - rect.top
    const height = rect.height
    const newVolume = Math.max(0, Math.min(1, 1 - (y / height)))

    volume.value = newVolume
    player.volume = newVolume

    if (newVolume > 0) {
      isMuted.value = false
      player.muted = false
    }
  }

  const handleMouseUp = () => {
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 全屏控制
const toggleFullscreen = () => {
  if (!player) return

  if (isFullscreen.value) {
    player.exitCssFullscreen()
  } else {
    player.getCssFullscreen()
  }
}

const initPlayer = () => {
  const container = document.getElementById('dplayer');
  const playerWarp = document.getElementById('dplayer-warp');
  if (!container) return;

  // 准备清晰度配置数据，确保 matchLiveClearList 存在且是数组
  const clearList = Array.isArray(liveDetail.matchLiveClearList) ? liveDetail.matchLiveClearList : [];
  const definitionListData = clearList.map(item => ({
    definition: item.liveName || '标清', // 清晰度标识
    url: item.liveUrl || '',        // 播放地址
    text: item.liveName || '标清'       // 显示文本
  }));

  // 更新自定义清晰度列表
  definitionList.value = definitionListData

  // 获取默认播放地址，优先使用高清，否则使用pullUrl
  let defaultUrl = '';
  let defaultDefinition = '高清';
  let defaultIndex = definitionListData.findIndex((item) => {
    return item.definition === '高清'
  })
  if (defaultIndex !== -1) {
    defaultUrl = definitionListData[defaultIndex].url
    defaultDefinition = definitionListData[defaultIndex].definition
  } else {
    defaultUrl = liveDetail.pullUrl || ''
    if (definitionListData.length > 0) {
      defaultDefinition = definitionListData[0].definition
    }
  }

  // 如果没有有效的播放地址，则不初始化播放器
  if (!defaultUrl) {
    console.warn('没有有效的播放地址，无法初始化播放器')
    return;
  }

  // 设置当前清晰度
  currentDefinition.value = defaultDefinition

  // 播放器配置 - 禁用原生控制栏和清晰度插件
  const playerConfig = {
    el: container,
    isLive: true,
    width: '860px',
    height: '500px',
    autoplay: true,
    playsinline: true,
    playbackRate: false,
    // autoplayMuted: true,
    autoplayMuted: false,
    fluid: true,
    lang: 'zh-cn',
    url: defaultUrl,
    controls: false,
    // 禁用原生控制栏和清晰度插件
    ignores: ['controls', 'definition', 'progress', 'play', 'volume', 'fullscreen', 'cssfullscreen'],
    fullscreenTarget: playerWarp as HTMLElement,
  }

  if (document.createElement('video').canPlayType('application/vnd.apple.mpegurl')) {
    // 原生支持 hls 播放
    player = new Player(playerConfig)
  } else if (HlsPlugin.isSupported()) {
    // 使用 HLS 插件
    player = new Player({
      ...playerConfig,
      controls: {
        autoHide: false
      },
      plugins: [HlsPlugin]
    })
  }

  // 添加事件监听
  if (player) {
    // 监听清晰度切换事件
    player.on('definition_change', (data: any) => {
      console.log('清晰度切换事件:', data)

      // 检查权限
      if (data && data.to && !checkQualityPermission(data.to.definition)) {
        // 阻止切换到蓝光
        message.warning('观看蓝光画质需要登录，请先登录')
        // 切换回之前的清晰度
        if (data.from && data.from.definition) {
          setTimeout(() => {
            const definitionPlugin = player.getPlugin('definition')
            if (definitionPlugin) {
              definitionPlugin.changeDefinition(data.from)
            }
          }, 100)
        }
        return
      }

      if (data && data.to) {
        message.success(`已切换到${data.to.definition}画质`)
      }
    })

    // 监听播放器就绪事件
    player.on('ready', () => {
      console.log('播放器初始化完成')
      // 初始化状态
      volume.value = player.volume || 0.6
      isMuted.value = player.muted || false
    })

    // 监听播放状态
    player.on('play', () => {
      isPlaying.value = true
      // 播放时启动自动隐藏
      // hideControlsAfterDelay()
    })

    player.on('pause', () => {
      isPlaying.value = false
      // 暂停时显示控制栏
      // showControls.value = true
      // resetControlsTimer()
    })

    player.on('error', (error: any) => {
      console.log('error---------', error)
    })

    // 监听音量变化
    player.on('volumechange', () => {
      volume.value = player.volume || 0
      isMuted.value = player.muted || false
    })

    // 监听全屏状态变化
    player.on('cssFullscreen_change', (data: any) => {
      isFullscreen.value = data
    })
    // 监听播放器聚焦
    // player.on(Events.PLAYER_FOCUS, (data: any) => {
    //   console.log(data, '--------------------')
    // })
  }
}
//获取统计数据
let matchStat = reactive<any>({
  homeBallControlRate: 0,
  awayBallControlRate: 0,
  awayRedCard: 0,
  awayYellowCard: 0,
  awayCornerKick: 0,
  homeShootRight: 0,
  awayShootRight: 0,
  homeDeflection: 0,
  awayDeflection: 0,
  homeCornerKick: 0,
  homeYellowCard: 0,
  homeRedCard: 0,
  homeAttack: 0,
  awayAttack: 0,
  homeDangerAttack: 0,
  awayDangerAttack: 0,
  homeScore: 0,
  awayScore: 0,
})
const getMatchStat = async () => {
  try {
    let res = await getMatchStatApi({ matchId: matchId.value })
    if (res.data) {
      matchStat = Object.assign(matchStat, res.data)
      matchStat.awayBallControlRate = parseInt(matchStat.awayBallControlRate) || 0
      matchStat.homeBallControlRate = parseInt(matchStat.homeBallControlRate) || 0
    }
  } catch (error) {
    console.error('获取比赛统计数据失败:', error)
  }
}

//获取比赛事件
const incidents = ref<any[]>([])
const getIncidents = async () => {
  try {
    let res = await getIncidentsApi({ matchId: matchId.value })
    // 确保 res.data 是数组，如果不是则使用空数组
    incidents.value = Array.isArray(res.data) ? res.data : [];
  } catch (error) {
    console.error('获取比赛事件失败:', error)
    incidents.value = [];
  }
}

//获取直播详情

let liveDetail = reactive<{
  awayLogo: string;
  awayName: string;
  homeLogo: string;
  homeName: string;
  hotMax: string;
  hotNum: number;
  liveCover: string;
  liveTitle: string;
  liveState: number; //0:未开播 1:开始 2：结束
  pullUrl: string;
  sclassName: string;
  userId: string;
  userName: string;
  userImage: string;
  collectLiveMax: number;
  collectId?: string;
  liveNotice?: string;
  awayScore?: number;
  homeScore?: number;
  matchTime?: string;
  liveCollectId?: number
  matchLiveClearList: MatchLiveClearItem[];
}>({
  awayLogo: '',
  awayName: '',
  homeLogo: '',
  homeName: '',
  hotMax: '',
  hotNum: 0,
  liveCover: '',
  liveTitle: '',
  liveState: 1,//主播主动设置开播状态 0:未开播 1:开始 2：结束
  pullUrl: '',
  sclassName: '',
  userId: '',
  userName: '',
  userImage: '',
  collectLiveMax: 0,
  collectId: '',//有值代表已经关注比赛,
  liveNotice: '',//公告-用于直播间提示
  matchLiveClearList: [{
    createTime: "",
    id: 0,
    liveName: "标清",
    liveType: "foot",
    liveUrl: "https://play.rwabwo.com/rrty/hd-zh-1-4249739_bqzm.flv?txSecret=8153b2f17441ed54da6d48d3725da2c9&txTime=686389AA",
    matchId: 0,
    screenshotUrl: ""
  }]
})
//获取带有拉流地址的直播详情
let recursionTime = ref<any>(null)
const isMatchStart = ref(false)
const getLiveDetails = async () => {
  clearTimeout(recursionTime.value)
  try {
    let res = await getLiveDetailsApi({ matchId: matchId.value, userId: userId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      liveDetail = Object.assign(liveDetail, res.data)
      // 延迟初始化，确保 DOM 已经渲染 判断直播状态1直播中 2 结束
      if (res.data.liveState === 1) {
        nextTick(() => {
          initPlayer()
        });
        //设置全局直播状态
        globaLiveState.value = true;
        intervalLiveState()
      } else if (res.data.liveState === 0) {
        //未开始
        recursionTime.value = setTimeout(() => {
          getLiveDetails()
        }, 30000)
      } else {
        //结束
        globaLiveState.value = false;
        // 清理直播状态定时器
        if (GlobalIntervalLiveState.value) {
          clearInterval(GlobalIntervalLiveState.value)
          GlobalIntervalLiveState.value = null
        }
        // getLivePage()
      }
    }
  } catch (error) {
    console.error('获取直播详情失败:', error)
    // 错误时重置状态
    globaLiveState.value = false;
    // 清理相关定时器
    if (GlobalIntervalLiveState.value) {
      clearInterval(GlobalIntervalLiveState.value)
      GlobalIntervalLiveState.value = null
    }
  }
}
//获取比分详情

interface MatchBaseInfoRes {
  /**
   * 客队logo
   */
  awayLogo?: string;
  /**
   * 客队名称
   */
  awayName?: string;
  /**
   * 客队总分
   */
  awayScore?: number;
  /**
   * 主队logo
   */
  homeLogo?: string;
  /**
   * 主队名称
   */
  homeName?: string;
  /**
   * 主队总分
   */
  homeScore?: number;
  /**
   * 比赛id
   */
  matchId?: number;
  /**
   * 比赛状态
   */
  matchState?: number; //0比赛异常 1未开始 2进行中 3已结束
  /**
   * 比赛时间
   */
  matchTime?: string;
  /**
   * 赛事logo
   */
  sclassLogo?: string;
  /**
   * 联赛名称
   */
  sclassName?: string;
  [property: string]: any;
}
let scoreDetail = reactive<MatchBaseInfoRes>({
  awayScore: 0,
  homeScore: 0,
  matchTime: '',
  matchState: 0,
  sclassName: '',
  awayName: '',
  homeName: '',
  awayLogo: '',
  homeLogo: '',
  sclassLogo: '',
  matchId: 0,
  homeId: 0,
  awayId: 0,
})
const getScoreDetails = async () => {
  try {
    let res = await getScoreDetailsApi({ matchId: matchId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      if (res.data.matchState === 1 || res.data.matchState === 2) { //1未开始 2进行中
        isMatchStart.value = true;
        intervalDetail()
      } else {
        isMatchStart.value = false;
        //清除定时器
        if (GlobalInterval.value) {
          clearInterval(GlobalInterval.value)
          GlobalInterval.value = null
        }
      }
      scoreDetail = Object.assign(scoreDetail, res.data)
    }
  } catch (error) {
    console.error('获取比分详情失败:', error)
    // 错误时也应该停止定时器和重置状态
    isMatchStart.value = false;
    if (GlobalInterval.value) {
      clearInterval(GlobalInterval.value)
      GlobalInterval.value = null
    }
  }
}
//获取直播状态
const globaLiveState = ref(false)
const getLiveState = async () => {
  try {
    let res = await getLiveDetailsApi({ matchId: matchId.value, userId: userId.value, liveTypeEnum: liveTypeEnum.value as string })
    if (res.data) {
      liveDetail.liveState = res.data.liveState;
      if (res.data.liveState === 3) {
        //直播结束
        if (GlobalIntervalLiveState.value) {
          clearInterval(GlobalIntervalLiveState.value)
          GlobalIntervalLiveState.value = null
        }
        globaLiveState.value = false;
        //销毁播放器
        destroyPlayer()
      }
    }
  } catch (error) {
    console.error('获取直播状态失败:', error)
    // 错误时也应该停止直播状态检查
    if (GlobalIntervalLiveState.value) {
      clearInterval(GlobalIntervalLiveState.value)
      GlobalIntervalLiveState.value = null
    }
    globaLiveState.value = false;
  }
}
const handleFollow = () => {
  if (liveDetail.liveCollectId) {
    cancelFollowUserApi({ liveId: userId.value }).then((res: any) => {
      if (res.code === 0) {
        liveDetail.liveCollectId = 0
        liveDetail.collectLiveMax -= 1
      }
    })
  } else {
    followUserApi({ liveId: userId.value }).then((res: any) => {
      if (res.code === 0) {
        liveDetail.liveCollectId = 1
        liveDetail.collectLiveMax += 1
      }
    })
  }
}
// const addHotNum = async () => {
//   await addHotNumApi({
//     matchId: matchId.value,
//     userId: userId.value
//   })
// }

const hotLiveList = ref<any[]>([])
//正在直播列表截取前4条
const homeLiveList = ref<any[]>([])
const getLivePage = async (size = 20) => {
  try {
    let res = await getLivePageApi({ current: 1, size })
    if (res.data && res.data.records && Array.isArray(res.data.records)) {
      hotLiveList.value = res.data.records;
      homeLiveList.value = res.data.records.slice(0, 2);
    } else {
      hotLiveList.value = [];
      homeLiveList.value = [];
    }
  } catch (error) {
    console.error('获取直播页面列表失败:', error)
    hotLiveList.value = [];
    homeLiveList.value = [];
  }
}
//定时刷新数据
let GlobalInterval = ref<NodeJS.Timeout | null>(null)
const intervalDetail = () => {
  safeStartInterval(() => {
    if (liveTypeEnum.value === 'foot') {
      //获取数据
      getMatchStat()
      //获取事件
      getIncidents()
    }
    //获取比分
    getScoreDetails()
  }, 60000, GlobalInterval)
}
//定时获取视频状态
let GlobalIntervalLiveState = ref<NodeJS.Timeout | null>(null)
const intervalLiveState = () => {
  safeStartInterval(() => {
    getLiveState()
  }, 60000, GlobalIntervalLiveState)
}
// 初始化数据的函数
const initData = () => {
  // 确保有有效的 matchId 和 userId 才进行 API 调用
  if (matchId.value > 0 && userId.value > 0) {
    // 获取直播详情
    getLiveDetails()
    // 获取比分详情
    getScoreDetails()

    // 如果是足球类型，获取统计数据和事件数据
    if (liveTypeEnum.value === 'foot') {
      getMatchStat()
      getIncidents()
    }

    //不是足球与篮球是请求热播列表
    // if (liveTypeEnum.value !== 'foot' && liveTypeEnum.value !== 'basket') {
    getLivePage()
    // }
  }
}
const destroyPlayer = () => {
  if (player) {
    player.destroy()
    player = null
  }
}

// 统一清理所有定时器的函数
const clearAllTimers = () => {
  // 清理比分详情定时器
  if (GlobalInterval.value) {
    clearInterval(GlobalInterval.value)
    GlobalInterval.value = null
  }
  // 清理直播状态定时器
  if (GlobalIntervalLiveState.value) {
    clearInterval(GlobalIntervalLiveState.value)
    GlobalIntervalLiveState.value = null
  }
  // 清理递归超时定时器
  if (recursionTime.value) {
    clearTimeout(recursionTime.value)
    recursionTime.value = null
  }
}

// 安全启动定时器的函数
const safeStartInterval = (callback: () => void, interval: number, timerRef: Ref<NodeJS.Timeout | null>) => {
  // 先清理现有定时器
  if (timerRef.value) {
    clearInterval(timerRef.value)
    timerRef.value = null
  }
  // 启动新定时器
  timerRef.value = setInterval(callback, interval)
}
// 清理资源的函数
const cleanupResources = () => {
  // 清理播放器
  destroyPlayer()
  if (hlsInstance) {
    hlsInstance.destroy()
    hlsInstance = null
  }
  // 清理所有定时器
  clearAllTimers()
}

// 重置数据状态的函数
const resetDataState = () => {
  // 重置比赛统计数据
  Object.assign(matchStat, {
    homeBallControlRate: 0,
    awayBallControlRate: 0,
    awayRedCard: 0,
    awayYellowCard: 0,
    awayCornerKick: 0,
    homeShootRight: 0,
    awayShootRight: 0,
    homeDeflection: 0,
    awayDeflection: 0,
    homeCornerKick: 0,
    homeYellowCard: 0,
    homeRedCard: 0,
    homeAttack: 0,
    awayAttack: 0,
    homeDangerAttack: 0,
    awayDangerAttack: 0,
    homeScore: 0,
    awayScore: 0,
    collectId: '',
    liveState: 0,
    liveNotice: '',
  })

  // 重置比分详情
  Object.assign(scoreDetail, {
    awayScore: 0,
    homeScore: 0,
    matchTime: '',
    matchState: 0,
    sclassName: '',
    awayName: '',
    homeName: '',
    awayLogo: '',
    homeLogo: '',
    sclassLogo: '',
    matchId: 0,
  })

  // 重置直播详情
  Object.assign(liveDetail, {
    awayLogo: '',
    awayName: '',
    homeLogo: '',
    homeName: '',
    hotMax: '',
    hotNum: 0,
    liveCover: '',
    liveTitle: '',
    liveState: 1,
    pullUrl: '',
    sclassName: '',
    userId: '',
    userName: '',
    userImage: '',
    collectLiveMax: 0,
    matchLiveClearList: [{
      createTime: "",
      id: 0,
      liveName: "标清",
      liveType: "foot",
      liveUrl: "https://play.rwabwo.com/rrty/hd-zh-1-4249739_bqzm.flv?txSecret=8153b2f17441ed54da6d48d3725da2c9&txTime=686389AA",
      matchId: 0,
      screenshotUrl: ""
    }]
  })

  // 重置其他状态
  incidents.value = []
  hotLiveList.value = []
  homeLiveList.value = []
  definitionList.value = []
  currentDefinition.value = '高清'
}

// 监听路由参数变化
watch([matchId, userId, liveTypeEnum], ([newMatchId, newUserId, newLiveTypeEnum], [oldMatchId, oldUserId, oldLiveTypeEnum]) => {
  // 当路由参数发生变化时
  if (newMatchId !== oldMatchId || newUserId !== oldUserId || newLiveTypeEnum !== oldLiveTypeEnum) {
    console.log('路由参数变化，重新加载数据:', {
      matchId: newMatchId,
      userId: newUserId,
      liveTypeEnum: newLiveTypeEnum
    })

    // 清理之前的资源
    cleanupResources()

    // 重置数据状态
    resetDataState()

    // 重置UI状态
    isMatchStart.value = false
    isShowScore.value = true
    showDefinitionList.value = false
    isPlaying.value = false
    isMuted.value = false
    showVolumeSlider.value = false
    isFullscreen.value = false

    // 重新初始化数据
    initData()
  }
}, { immediate: false })

onMounted(() => {
  // 初始化数据
  initData()
  // 添加点击外部关闭清晰度列表的事件监听
  document.addEventListener('click', handleClickOutside)
});

// 组件卸载时清理资源
onUnmounted(() => {
  // 清理资源
  cleanupResources()
  // 移除点击外部关闭清晰度列表的事件监听
  document.removeEventListener('click', handleClickOutside)
});
</script>

<style lang="scss" scoped>
:deep(.n-card__content) {
  padding: 0px 10px;
}

:deep(.n-card > .n-card__content:first-child) {
  padding-top: 0;
}

:deep(.xgplayer-controls) {
  display: none;
}

// .tab-box {
//   .n-tabs-tab__label {
//     font-size: 24px;
//   }
// }

:deep(.tab-content .n-tabs .n-tabs-tab .n-tabs-tab__label) {
  font-size: 20px;
}

.line-up-box {
  min-height: 800px;
}

.detail-page {
  width: 100%;
  min-height: 100vh;
  background: #F3F3F3;

  .live-container {
    height: 670px;
    padding: 10px 0;
    background-image: url('@/static/img/bg.jpg');
    background-position: center;
    background-size: cover;

    .live-content {
      width: 1200px;
      height: 650px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;

      .video-box {
        width: 860px;
        height: 100%;
        background-color: #000;
        display: flex;
        flex-direction: column;

        .header {
          display: flex;
          padding: 16px 10px;
          background-color: #ffffff;

          .logo-box {
            .logo {
              width: 60px;
              height: 60px;
              display: block;
              border-radius: 50%;
            }
          }

          .info-box {
            display: flex;
            flex-direction: column;
            height: 60px;
            flex: 1;
            justify-content: space-between;
            padding-left: 10px;

            .title {
              font-size: 16px;
              color: #333;
            }

            .other {
              font-size: 14px;
              display: flex;
              align-items: center;
              justify-content: space-between;

              .left {
                display: flex;
              }

              .right {
                display: flex;
              }

              .name {
                color: #333;
                margin-right: 12px;
              }

              .number {
                color: #818181;
                margin-right: 12px;
              }

              .load {
                color: #818181;
                cursor: pointer;
                display: flex;
                align-items: center;

                .icon {
                  display: block;
                  margin-right: 2px;
                  width: 16px;
                  height: 16px;
                }
              }

              .viewer-count {
                color: #818181;
                display: flex;
                align-items: center;
                margin-right: 20px;

                .hot {
                  display: block;
                  width: 12px;
                  height: 13px;
                  margin-right: 2px;
                }
              }

              .fllow-box {
                display: flex;
                background-color: #E7E7E7;
                color: #333333;
                border-radius: 20px;
                overflow: hidden;

                .num {
                  padding: 4px 10px;
                }

                .text {
                  background-color: #FB2B1F;
                  color: #ffffff;
                  padding: 4px 10px;
                  border-radius: 20px;
                  cursor: pointer;
                }
              }
            }

          }
        }

        .player-box {
          position: relative;
          flex: 1;
          // max-height: 500px;
          // background-color: #ffffff;
          // overflow: hidden;

          &.xgplayer-fullscreen-parent {
            position: fixed;
          }

          .xgplayer-controls {
            display: none;
          }

          .cancel-muted {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            bottom: 90px;
            width: 180px;
            height: 56px;
            background: #ea5f3d;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 901;
            cursor: pointer;

            .t {
              margin-left: 20px;
              font-size: 14px;
              color: #fff;
            }

            .icon {
              width: 30px;
              display: block;
            }

            &:hover {
              background: #FB2B1F;
            }
          }

          /* 自定义控制栏样式 */
          .custom-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            // background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
            background-color: rgba(0, 0, 0, 0.3);
            padding: 6px 16px;
            // transition: all 0.3s ease;
            z-index: 100;
            opacity: 0;
            // transform: translateY(100%);
          }

          &:hover {
            .custom-controls {
              opacity: 1;
              // transform: translateY(0);
            }

          }

          // .custom-controls.hide {
          //   opacity: 0;
          //   // transform: translateY(100%);
          // }

          // .custom-controls.show {
          //   opacity: 1;
          //   // transform: translateY(0);
          // }
          .live-end-warp {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .tip {
              color: #fff;
              font-size: 26px;
              margin-bottom: 20px;
            }

            .sub-box {
              display: flex;
              gap: 30px;
            }
          }
        }

        .footer-box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0px 10px;
          flex: 1;
          background-color: #ffffff;
          font-size: 14px;

          .left-box {
            display: flex;
            align-items: center;

            .live-badge {
              display: flex;
              align-items: center;
              background-color: #FB2B1F;
              color: #ffffff;
              padding: 2px 6px;
              border-radius: 6px;
              font-size: 14px;
              margin-right: 10px;

              .live-dot {
                display: block;
                width: 16px;
                height: 16px;
                margin-right: 4px;
              }
            }

            span {
              color: #818181;
              font-size: 14px;
              margin-right: 10px;
            }
          }

          .right-box {
            display: flex;
            align-items: center;

            .sub-box {
              display: flex;
              align-items: center;
            }

            .team {
              display: flex;
              align-items: center;
              margin-right: 10px;

              .logo {
                display: block;
                width: 24px;
                height: 24px;
                margin: 0px 4px;
              }

              span {
                color: #333;
                font-size: 14px;
              }
            }

            .score {
              display: flex;
              align-items: center;
              margin-right: 10px;

              .num {
                color: #FB2B1F;
                font-size: 14px;
                margin: 0 10px;
              }
            }

            .btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 76px;
              height: 26px;
              border: 1px solid #FB2B1F;
              color: #FB2B1F;
              font-size: 12px;
              border-radius: 4px;
              cursor: pointer;
            }
          }
        }
      }

      .msg-box {
        width: 320px;
        height: 100%;
        background-color: #ffffff;
        display: flex;
        flex-direction: column;

        .notice {
          padding: 10px;
          font-size: 14px;
          color: #818181;

          .label {
            color: #333;
          }
        }

        .tab-content-wrap {
          flex: 1;
          overflow: auto;
        }

        .tabs {
          display: flex;
          gap: 50px;
          align-items: center;
          height: 40px;
          line-height: 40px;
          padding: 0 20px;
          font-size: 16px;
          color: #333;
          cursor: pointer;
          border-top: 2px solid #E6E6E6;
          border-bottom: 2px solid #E6E6E6;

          .tabs-item {

            &:hover {
              color: #FB2B1F;
            }
          }

          .active {
            color: #FB2B1F;
            position: relative;

            &::after {
              position: absolute;
              content: "";
              bottom: 0;
              left: 15%;
              right: 15%;
              height: 2px;
              background: #FB2B1F;
            }
          }
        }
      }
    }
  }

  /* 控制按钮区域 */
  .controls-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .controls-left,
  .controls-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .control-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #fff;
    backdrop-filter: blur(8px);
  }

  // .control-btn:hover {
  //   background: rgba(255, 255, 255, 0.2);
  // }

  // .control-btn.active {
  //   background: rgba(0, 212, 255, 0.3);
  // }

  /* 播放按钮特殊样式 */
  // .play-btn {
  //   width: 44px;
  //   height: 44px;
  //   background: rgba(255, 255, 255, 0.15);
  // }

  .play-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    // transform: scale(1.05);
  }

  /* 音量控制 */
  .volume-control {
    position: relative;
    display: flex;
    align-items: center;
  }

  .volume-slider {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    // margin-bottom: 8px;
    background: rgba(0, 0, 0, 0.9);
    border-radius: 20px;
    padding: 12px 8px;
    backdrop-filter: blur(12px);
  }

  .volume-track {
    position: relative;
    width: 4px;
    height: 80px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    cursor: pointer;
  }

  .volume-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(0deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    transition: height 0.2s ease;
  }

  .volume-thumb {
    position: absolute;
    left: 50%;
    width: 12px;
    height: 12px;
    background: #fff;
    border-radius: 50%;
    transform: translateX(-50%);
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
  }

  .volume-thumb:hover {
    transform: translateX(-50%) scale(1.2);
  }

  /* 清晰度控制 */
  .definition-control {
    position: relative;
  }

  .definition-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    color: #fff;
    transition: all 0.3s ease;
    backdrop-filter: blur(8px);
    min-width: 50px;
    justify-content: center;
  }

  .definition-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    // transform: scale(1.05);
  }

  // .definition-btn.active {
  // background: rgba(0, 212, 255, 0.3);
  // }

  .definition-list {
    position: absolute;
    bottom: 100%;
    right: 0;
    margin-bottom: 8px;
    background: rgba(0, 0, 0, 0.95);
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    // border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px);
    min-width: 110px;
  }

  .definition-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 14px;
    color: #fff;

    font-size: 13px;
    cursor: pointer;
    transition: all 0.25s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);

    .login-tag {
      font-size: 12px;
      color: #FB2B1F;
      // background: #FB2B1F;
      // padding: 2px 4px;
      // border-radius: 4px;
    }
  }

  .definition-item:last-child {
    border-bottom: none;
  }

  .definition-item:hover {
    background: rgba(255, 255, 255, 0.12);
  }

  .definition-item.selected {
    // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-color: #FB2B1F;
    color: #fff;
    font-weight: 600;
  }

  // .definition-item.premium {
  //   color: #333;
  //   font-weight: 600;
  // }

  .check-icon {
    color: #fff;
    font-weight: bold;
    font-size: 14px;
  }

  // .definition-item.premium .check-icon {
  //   color: #333;
  // }

  /* 过渡动画 */
  // .definition-fade-enter-active,
  // .definition-fade-leave-active,
  // .volume-fade-enter-active,
  // .volume-fade-leave-active {
  //   transition: all 0.3s ease;
  // }

  // .definition-fade-enter-from,
  // .volume-fade-enter-from {
  //   opacity: 0;
  //   transform: translateY(10px) scale(0.95);
  // }

  // .definition-fade-leave-to,
  // .volume-fade-leave-to {
  //   opacity: 0;
  //   transform: translateY(10px) scale(0.95);
  // }

  /* 移动端适配 */
  @media (max-width: 768px) {
    .custom-controls {
      padding: 16px 12px 12px;
    }

    .controls-left,
    .controls-right {
      gap: 8px;
    }

    .control-btn {
      width: 32px;
      height: 32px;
    }

    .play-btn {
      width: 40px;
      height: 40px;
    }

    .definition-btn {
      padding: 6px 10px;
      font-size: 12px;
      min-width: 45px;
    }

    .volume-slider {
      display: none;
      /* 移动端隐藏音量滑块 */
    }

    .time-display {
      font-size: 11px;
    }
  }

  .data-container {
    width: 1200px;
    margin: 0 auto;
    padding-bottom: 20px;

    .basketball-data {
      background-color: #ffffff;
      padding: 30px 20px;
      display: flex;
      justify-content: space-between;

      .score-box {
        display: flex;
        width: 60%;
        box-sizing: border-box;
        padding: 0 50px;

        .score-item {
          display: flex;
          flex-direction: column;

          .label,
          .data {
            color: #818181;
            font-size: 12px;
            margin-bottom: 16px;
          }

          .data {
            &.top {
              color: #333333;
            }
          }
        }

        .sub-score {
          flex: 1;
          padding-left: 60px;
          display: flex;
          justify-content: space-around;
        }
      }

      .stat-box {
        background-color: #F4F4F4;
        display: flex;
        justify-content: space-between;
        flex: 1;
        padding: 0 60px;
        border-radius: 4px;

        .team {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .logo {
            width: 20px;
            height: 20px;
            display: block;
            margin-bottom: 10px;
          }

          .name {
            color: #333;
            font-size: 14px;
          }
        }

        .content {
          display: flex;
          flex: 1;
          justify-content: center;
          align-items: center;
          flex-direction: column;

          .label {
            color: #818181;
            font-size: 14px;
            margin: 0 20px;
          }

          .number {
            color: #333;
            font-size: 14px;
          }
        }
      }
    }

    .base-data {
      display: flex;
      background-color: #ffffff;
      padding: 30px 0;
      align-items: center;

      .chart-box {
        width: 50%;
        display: flex;
        padding: 0 20px;

        .chart-item {
          flex: 1;
          text-align: center;
          padding: 0 12px;
          box-sizing: border-box;

          .chart-progress {
            width: 78px;
            height: 78px;
          }

          .name {
            // color: #818181
            color: #333;
            font-size: 16px;
            margin-bottom: 4px;
          }

          .chart {
            display: flex;
            justify-content: center;
            align-items: center;


            .number {
              font-size: 16px;
              color: #333;
              margin: 0 4px;
            }
          }

          .data {
            display: flex;
            justify-content: space-around;
            margin-top: 8px;


            .item {
              display: flex;
              align-items: center;
              font-size: 12px;
              color: #333;

              .number {
                font-size: 14px;
                margin: 0 4px;
              }
            }
          }

          .text {
            margin-top: 8px;
            font-size: 16px;
          }
        }
      }

      .shoot-box {
        width: 50%;
        padding: 0 20px;

        .shoot-number-box {
          border-top: 2px solid #fff;
          border-left: 2px solid #fff;
          border-right: 2px solid #fff;
          border-top-left-radius: 12px;
          border-top-right-radius: 12px;
          background-color: #F5F8FF;
          padding: 0 16px;
          color: #333;

          .active {
            color: #FB2B1F;
          }


          .number-data {
            display: flex;
            justify-content: space-around;
            padding: 16px 0;

            .home {
              font-size: 18px;
              font-weight: 600;
            }

            .text {
              font-size: 16px;
            }

            .visiting {
              font-size: 18px;
              font-weight: 600;
            }
          }

          .shoot-off-box {
            border-top: 2px solid #fff;
            border-left: 2px solid #fff;
            border-right: 2px solid #fff;
            border-top-left-radius: 12px;
            border-top-right-radius: 12px;
            background-color: #E1EBFF;

            .off-data {
              display: flex;
              justify-content: space-around;
              padding: 16px 0;

              .home {
                font-size: 18px;
                font-weight: 600;
              }

              .text {
                font-size: 16px;
              }

              .visiting {
                font-size: 18px;
                font-weight: 600;
              }
            }

            .shoot-on-box {
              width: 160px;
              margin: 0 auto;
              border-top: 2px solid #fff;
              border-left: 2px solid #fff;
              border-right: 2px solid #fff;
              border-top-left-radius: 12px;
              border-top-right-radius: 12px;
              background-color: #C7DAFF;

              .on-data {
                display: flex;
                justify-content: space-around;
                padding: 16px 0;

                .home {
                  font-size: 18px;
                  font-weight: 600;
                }

                .text {
                  font-size: 16px;
                }

                .visiting {
                  font-size: 18px;
                  font-weight: 600;
                }

              }
            }
          }
        }
      }
    }

    .tab-content {
      margin-top: 20px;
    }

    .hot-section {
      margin-top: 20px;
      padding-bottom: 30px;

      .public-title {
        display: flex;
        align-items: center;

        .title {
          font-size: 30px;
          color: #333;
          font-weight: 500;
        }

        margin-bottom: 18px;
      }

      .list-content {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
      }
    }
  }
}
</style>
