<template>
  <div class="user-dashboard">
    <!-- 右侧内容区域 -->
    <div class="user-info-section">
      <div class="user-avatar" @click="handleChangeAvatar">
        <img :src="userInfo.userImage || defaultAvatar" alt="用户头像" />
        <!-- 浮动编辑头像 -->
        <div class="avatar-overlay">
          <img src="@/static/user/camera.png" alt="编辑" class="edit-icon" />
        </div>
      </div>
      <div class="user-details">
        <h3 class="username">{{ userInfo.userName || '哩哩哩哩' }}</h3>
        <div class="progress-section">
          <div class="progress-info">
            <span class="level-badge">Lv.{{ userLevel }}</span>
            <div class="progress-bar">
              <n-progress type="line" :percentage="currentExperience" :height="10" color="#FB2B1F"
                :show-indicator="false" processing />
            </div>
            <span class="progress-text">{{ experienceText }}</span>
          </div>
        </div>
        <div class="stats-section">
          <div class="stat-item">
            <div class="stat-value">LV.{{ userLevel }}</div>
            <div class="stat-label">等级</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userInfo.diamond || 1 }}</div>
            <div class="stat-label">经验值</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ userCoins || 1 }}</div>
            <div class="stat-label">金币</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 账号管理区域 -->
    <div class="account-management">
      <h3 class="section-title">账号管理</h3>
      <div class="management-items">
        <div class="management-item">
          <div class="item-info">
            <div class="item-icon phone-icon">
              <img src="@/static/user/<EMAIL>" />
            </div>
            <div class="item-details">
              <div class="item-title">我的手机</div>
              <div class="item-desc">{{ phoneDisplay }}</div>
            </div>
          </div>
          <n-button type="primary" size="small" class="action-btn" @click="handleChangePhone">
            更换手机
          </n-button>
        </div>

        <div class="management-item">
          <div class="item-info">
            <div class="item-icon password-icon">
              <img src="@/static/user/<EMAIL>" />
            </div>
            <div class="item-details">
              <div class="item-title">我的密码</div>
              <div class="item-desc">已设置登录密码</div>
            </div>
          </div>
          <n-button type="primary" size="small" class="action-btn" @click="handleChangePassword">
            更改密码
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useMessage } from 'naive-ui'
import { useRouter } from 'vue-router'
import { uploadImageH5 } from '@/common/upload'
import { updateHeadImgApi } from '@/api/user'

const router = useRouter()
const userStore = useUserStore()
const userInfo = computed(() => userStore.getUserInfo)
const message = useMessage()

// 响应式数据
const defaultAvatar = '/src/static/header/<EMAIL>'
const userLevel = ref(1)
const userCoins = ref(1)
const currentExperience = ref(1)
const maxExperience = ref(100)

// 计算属性
// const progressPercentage = computed(() => {
//   return (currentExperience.value / maxExperience.value) * 100
// })

const experienceText = computed(() => {
  return `${currentExperience.value}/${maxExperience.value}`
})

const phoneDisplay = computed(() => {
  return '已设置手机号码'
})

// 修改用户头像
const handleChangeAvatar = async () => {
  try {
    // 调用上传图片方法，传入ossEnum参数
    const { data: result }: any = await uploadImageH5('HEAD_IMG')
    if (result && result.data) {
      // 调用更新头像API
      await updateHeadImgApi({ imgUrl: result.data })
      // 更新用户信息
      await userStore.updateUserInfo()
      message.success('头像更新成功')
    }
  } catch (error) {
    message.error('头像上传失败')
    console.error('头像上传错误:', error)
  }
}

// 处理更换手机号
const handleChangePhone = () => {
  router.push('/user/modify-phone')
}

// 处理更改密码
const handleChangePassword = () => {
  router.push('/user/modify-password')
}

onMounted(() => {
  userStore.updateUserInfo()
})
</script>

<style lang="scss" scoped>
.user-info-section {
  background: #ffffff;
  padding: 40px;
  display: flex;
  align-items: center;
  flex-direction: column;

  .user-avatar {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 10px;
    background: #f0f0f0;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 50%;

      .edit-icon {
        width: 48px;
        height: 48px;
      }
    }

    &:hover .avatar-overlay {
      opacity: 1;
    }
  }

  .user-details {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .username {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
    }

    .progress-section {
      margin-bottom: 20px;

      .progress-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .level-badge {
          background: #FB2B1F;
          color: #ffffff;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 14px;
          font-weight: 600;
          min-width: 50px;
          text-align: center;
        }

        .progress-bar {
          width: 400px;
        }

        .progress-text {
          font-size: 14px;
          color: #666;
          min-width: 60px;
        }
      }
    }

    .stats-section {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 0 40px;


      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}

.account-management {
  background: #ffffff;
  padding: 40px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 30px 0;
  }

  .management-items {
    .management-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .item-info {
        display: flex;
        align-items: center;

        .item-icon {
          margin-right: 16px;

          img {
            width: 40px;
            height: 40px;
          }

          .icon-placeholder {
            font-size: 18px;
          }
        }

        .item-details {
          .item-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .item-desc {
            font-size: 14px;
            color: #666;
          }
        }
      }

      .action-btn {
        background: #FB2B1F !important;
        border-color: #FB2B1F !important;

        &:hover {
          background: #e02419 !important;
          border-color: #e02419 !important;
        }
      }
    }
  }
}
</style>